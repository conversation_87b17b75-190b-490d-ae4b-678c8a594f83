/**
 * WebSDK - 主要的SDK管理器
 * 负责初始化、配置管理和组件协调
 */

// 处理器已移除，改为直接事件总线通信

import { JsonRpcActionHandler, ActionCallback } from '../jsonrpc/JsonRpcActionHandler';
import { JsonRpcMessageHandler } from '../jsonrpc/JsonRpcMessageHandler';
import {
  JsonRpcNotificationManager,
  NotificationCallback,
} from '../jsonrpc/JsonRpcNotificationManager';
import { JsonRpcRequestManager, RequestOptions } from '../jsonrpc/JsonRpcRequestManager';
import { MessageRouter } from '../routing/MessageRouter';
import { AIClient } from '../services/AIClient';
import { DigitalHumanController } from '../services/DigitalHumanController';
import { HKSTTClient } from '../services/HKSTTClient';
import { MessageService } from '../services/MessageService';
import { NotificationService } from '../services/NotificationService';
import { ServiceCoordinator } from '../services/ServiceCoordinator';
// JsonRpcTransportAdapter已移除，使用轻量级实现
import { TransportManager } from '../transport/TransportManager';
import {
  WebSDKConfig,
  InternalWebSDKConfig,
  createInternalConfig,
  validateConfig,
} from '../types/config';
import { GreetingPageManager } from '../ui/GreetingPageManager';
import { ErrorHandler } from '../utils/ErrorHandler';
import { Logger, LogLevel } from '../utils/Logger';
import '../types/global';

import { EventBus } from './EventBus';

// JSON-RPC 请求参数类型定义
interface SpeakRequestParams {
  text: string;
  delay?: number;
  display?: boolean;
}

interface UpdateBackgroundInfoParams {
  sessionId: string;
  page: string;
  status: string;
}

interface AddMessagesParams {
  sessionId: string;
  messages: unknown[];
}

interface PushBizDataParams {
  key: string;
  data: unknown;
}

/**
 * SDK状态
 */
export interface SDKStatus {
  isInitialized: boolean;
  isConnected: boolean;
  isReady: boolean; // SDK完全就绪状态
  connectionUrl: string;
  activeSessions: number;
  totalMessages: number;
  lastActivity: number;
}

/**
 * WebSDK主类
 */
export class WebSDK {
  private static instance: WebSDK | null = null;

  private config: InternalWebSDKConfig;
  private logger: Logger;
  private isInitialized = false;
  private isConnected = false;
  private isReady = false; // SDK完全就绪状态（初始化+连接+组件就绪）

  // 核心组件
  private eventBus!: EventBus;
  private jsonRpcHandler!: JsonRpcMessageHandler;

  // JSON-RPC管理器（模块化）
  private requestManager!: JsonRpcRequestManager;
  private actionHandler!: JsonRpcActionHandler;
  private notificationManager!: JsonRpcNotificationManager;

  private messageRouter!: MessageRouter;

  // 处理器（已移除，改为直接事件总线通信）

  // 服务
  private messageService!: MessageService;
  private notificationService!: NotificationService;

  // 传输和服务客户端
  private transportManager!: TransportManager;
  private hksttClient!: HKSTTClient;
  private aiClient!: AIClient;
  private serviceCoordinator!: ServiceCoordinator;
  private digitalHumanController!: DigitalHumanController;

  // UI管理
  private greetingPageManager!: GreetingPageManager;

  private constructor(config: WebSDKConfig) {
    // 验证并创建内部配置
    validateConfig(config);
    this.config = createInternalConfig(config);

    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'WebSDK',
    });

    // 初始化核心组件
    this.initializeCore();
  }

  /**
   * 获取SDK实例（单例模式）
   */
  public static getInstance(config?: WebSDKConfig): WebSDK {
    if (!WebSDK.instance) {
      if (!config) {
        throw ErrorHandler.createValidationError('首次调用getInstance时必须提供配置');
      }
      WebSDK.instance = new WebSDK(config);
    }
    return WebSDK.instance;
  }

  /**
   * 重置SDK实例（主要用于测试）
   */
  public static resetInstance(): void {
    if (WebSDK.instance) {
      WebSDK.instance.destroy();
      WebSDK.instance = null;
    }
  }

  /**
   * 初始化核心组件
   */
  private initializeCore(): void {
    try {
      this.logger.info('初始化SDK核心组件');

      // 创建事件总线
      this.eventBus = new EventBus();

      // 创建JSON-RPC消息处理器
      this.jsonRpcHandler = new JsonRpcMessageHandler();

      // 复杂的传输适配器已移除，使用轻量级实现

      // 创建传输管理器 - 使用固定的合理默认值
      this.transportManager = new TransportManager(
        {
          websocket: {
            url: this.config.hksttUrl,
            connectionTimeout: 10000,
            maxReconnectAttempts: 5,
            reconnectInterval: 3000,
          },
          http: {
            baseURL: this.config.aiServerUrl,
            timeout: 30000,
            debug: this.config.debug,
          },
          sse: {
            baseURL: this.config.aiServerUrl,
            timeout: 30000,
            debug: this.config.debug,
          },
          debug: this.config.debug,
        },
        this.eventBus
      );

      // 更新传输适配器的传输层实例
      // 注意：这里需要访问TransportManager的内部传输层实例
      // 实际实现中可能需要TransportManager提供getter方法

      // 复杂的JSON-RPC客户端已移除，使用轻量级实现

      // 创建服务客户端 - 使用固定的合理默认值
      this.hksttClient = new HKSTTClient(
        {
          url: this.config.hksttUrl,
          connectionTimeout: 10000,
          maxReconnectAttempts: 5,
          reconnectInterval: 3000,
          debug: this.config.debug,
        },
        this.eventBus
      );

      this.aiClient = new AIClient(
        {
          url: this.config.aiServerUrl,
          timeout: 30000,
          debug: this.config.debug,
        },
        this.eventBus
      );

      // 创建JSON-RPC管理器（模块化）
      this.requestManager = new JsonRpcRequestManager(this.eventBus);
      this.actionHandler = new JsonRpcActionHandler(this.eventBus);
      this.notificationManager = new JsonRpcNotificationManager(this.eventBus);

      // 创建消息路由器
      this.messageRouter = new MessageRouter(
        {
          defaultScenario: 'greeting-page',
          debug: this.config.debug,
        },
        this.eventBus
      );

      // 处理器已移除，改为直接事件总线通信

      // 创建服务
      this.messageService = new MessageService(this.eventBus);
      this.notificationService = new NotificationService(this.eventBus);

      // 创建服务协调器
      const coordinatorConfig: any = { debug: this.config.debug };

      // 如果提供了TTS URL，则配置TTS服务
      if (this.config.ttsUrl) {
        // 根据URL格式判断协议类型
        const protocol =
          this.config.ttsUrl.startsWith('ws://') || this.config.ttsUrl.startsWith('wss://')
            ? 'websocket'
            : 'http-sse';

        coordinatorConfig.tts = {
          serverUrl: this.config.ttsUrl,
          protocol: protocol,
          debug: this.config.debug,
          // HTTP+SSE 特定配置
          requestTimeout: 30000,
          maxRetryAttempts: 3,
          retryDelay: 1000,
          seed: 42,
        };
        this.logger.info('TTS服务已配置', {
          ttsUrl: this.config.ttsUrl,
          protocol: protocol,
        });
      }

      this.serviceCoordinator = new ServiceCoordinator(
        coordinatorConfig,
        this.eventBus,
        this.hksttClient,
        this.aiClient
      );

      // 创建数字人控制器
      this.digitalHumanController = new DigitalHumanController({
        eventBus: this.eventBus,
        debug: this.config.debug,
      });

      // 创建打招呼页面管理器
      this.greetingPageManager = new GreetingPageManager(this);

      // 注册处理器到路由器
      this.registerHandlers();

      // 绑定事件
      this.bindEvents();

      this.isInitialized = true;
      this.logger.info('SDK核心组件初始化完成');
    } catch (error) {
      this.logger.error('SDK初始化失败', error);
      throw ErrorHandler.wrapError(error as Error);
    }
  }

  /**
   * 注册处理器（JSON-RPC路由已移除，改为直接事件总线通信）
   */
  private registerHandlers(): void {
    // JSON-RPC路由已移除，处理器现在通过事件总线直接通信
    this.logger.info('处理器注册完成（使用事件总线直接通信）');
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 极简版本 - 只绑定核心事件

    // 绑定传输管理器事件
    this.eventBus.on('transport:all-transports-ready', () => {
      this.logger.info('所有传输层已就绪');
    });

    // 绑定HKSTT客户端事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 事件数据结构复杂且可变
    this.eventBus.on('hkstt-client:state-change', (data: any) => {
      this.logger.info('HKSTT客户端状态变化', data);
    });

    // 绑定AI客户端事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 事件数据结构复杂且可变
    this.eventBus.on('ai:chat-response', (data: any) => {
      this.logger.info('收到AI聊天响应', data);
    });

    // 绑定连接状态事件
    this.eventBus.on('connection:status', (data: unknown) => {
      const status = data as { connected: boolean };
      this.isConnected = status.connected;
      this.logger.info('连接状态变化', { connected: this.isConnected });
    });

    // 注意：人脸检测和用户检测应该通过JSON-RPC通知机制处理
    // 这些事件应该作为 notifications/faceStatus 和 notifications/newUser 通知
    // 而不是在WebSDK层直接处理
    // TODO: 重构为标准的JSON-RPC通知处理

    // 绑定客户端JSON-RPC请求处理
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- JSON-RPC 请求格式多样
    this.eventBus.on('client:send-request', (request: any) => {
      this.handleClientRequest(request);
    });

    // JSON-RPC处理已模块化，由各个管理器自动处理

    // 客户端通知发送处理已模块化
  }

  /**
   * 启动SDK（内部方法，仅供init函数使用）
   */
  public async _internalStart(): Promise<void> {
    if (!this.isInitialized) {
      throw ErrorHandler.createSystemError('SDK未初始化');
    }

    try {
      this.logger.info('启动SDK', {
        hksttUrl: this.config.hksttUrl,
        aiServerUrl: this.config.aiServerUrl,
      });

      // 启动传输管理器
      await this.transportManager.start();

      // 连接HKSTT客户端
      await this.hksttClient.connect();

      this.isConnected = true;

      // 设置SDK为完全就绪状态（在初始化组件之前）
      this.isReady = true;

      // 发送SDK就绪事件
      this.eventBus.emit('sdk:ready');

      // 监听SDK内部事件
      this.bindSDKEvents();

      // 初始化所有已注册的组件（此时SDK已就绪）
      this.initializeRegisteredComponents();

      this.logger.info('SDK启动完成');
    } catch (error) {
      this.logger.error('SDK启动失败', error);
      throw ErrorHandler.wrapError(error as Error);
    }
  }

  /**
   * 停止SDK
   */
  public stop(): void {
    this.logger.info('停止SDK');

    // 停止传输管理器
    this.transportManager.stop();

    // 断开服务客户端
    this.hksttClient.disconnect();
    this.aiClient.stopAllStreams();

    this.isConnected = false;
    this.isReady = false; // SDK不再就绪
    this.logger.info('SDK已停止');
  }

  /**
   * 检查SDK是否完全就绪
   */
  public isSDKReady(): boolean {
    return this.isReady;
  }

  /**
   * 获取SDK状态
   */
  public getStatus(): SDKStatus {
    let activeSessions = 0;
    let totalMessages = 0;

    if (this.isInitialized) {
      // 获取会话统计（SessionCoordinator可能没有getStats方法，使用基本统计）
      activeSessions = 1; // 简化统计

      // 获取消息统计
      if (this.messageService) {
        const messageStats = this.messageService.getStats();
        totalMessages = messageStats.totalMessages;
      }
    }

    return {
      isInitialized: this.isInitialized,
      isConnected: this.isConnected,
      isReady: this.isReady,
      connectionUrl: this.config.hksttUrl,
      activeSessions,
      totalMessages,
      lastActivity: Date.now(),
    };
  }

  /**
   * 获取事件总线（供外部组件使用）
   */
  public getEventBus(): EventBus {
    return this.eventBus;
  }

  // 复杂的JSON-RPC客户端已移除，使用getSimpleJsonRpcClient()代替

  /**
   * 获取静态文件基础路径
   */
  public getStaticBasePath(): string {
    return this.config.staticBasePath;
  }

  /**
   * 安全地注册事件监听器（确保SDK就绪后再注册）
   */
  public onReady(callback: () => void): void {
    if (this.isReady) {
      callback();
    } else {
      // 监听SDK就绪事件
      this.eventBus.on('sdk:ready', callback);
    }
  }

  // 复杂的通知监听器已移除，使用EventBus直接监听

  // 复杂的request和notify方法已移除，使用simpleRequest()代替

  // simpleRequest和getSimpleJsonRpcClient方法已移除，使用增强的sendRequest方法

  /**
   * 获取消息服务（供外部组件使用）
   */
  public getMessageService(): MessageService {
    return this.messageService;
  }

  // JSON-RPC服务器已移除

  /**
   * 获取通知服务（供外部组件使用）
   */
  public getNotificationService(): NotificationService {
    return this.notificationService;
  }

  /**
   * 获取传输管理器（供外部组件使用）
   */
  public getTransportManager(): TransportManager {
    return this.transportManager;
  }

  /**
   * 获取HKSTT客户端（供外部组件使用）
   */
  public getHKSTTClient(): HKSTTClient {
    return this.hksttClient;
  }

  /**
   * 获取AI客户端（供外部组件使用）
   */
  public getAIClient(): AIClient {
    return this.aiClient;
  }

  /**
   * 获取服务协调器（供外部组件使用）
   */
  public getServiceCoordinator(): ServiceCoordinator {
    return this.serviceCoordinator;
  }

  /**
   * 获取消息路由器（供外部组件使用）
   */
  public getMessageRouter(): MessageRouter {
    return this.messageRouter;
  }

  // 移除了 handleNewUserDetected 和 handleFaceStatusChange 方法
  // 这些应该通过标准的JSON-RPC通知机制处理：
  // - notifications/faceStatus
  // - notifications/newUser
  //
  // 正确的处理方式是：
  // 1. 服务器发送JSON-RPC通知
  // 2. JsonRpcNotificationManager接收并分发
  // 3. 用户通过 sdk.onNotification('notifications/faceStatus', callback) 监听

  /**
   * 初始化所有已注册的组件
   */
  private initializeRegisteredComponents(): void {
    if (!window.webSDKComponents) {
      this.logger.info('没有找到已注册的组件');
      return;
    }

    this.logger.info(`开始初始化 ${window.webSDKComponents.length} 个已注册的组件`);

    window.webSDKComponents.forEach((component, index) => {
      try {
        this.logger.info(
          `初始化组件 ${index + 1}/${window.webSDKComponents?.length || 0}: ${component.type}`
        );
        component.initialize(this);
      } catch (error) {
        this.logger.error(`组件 ${component.type} 初始化失败`, error);
      }
    });

    this.logger.info('所有组件初始化完成');
  }

  /**
   * 显示打招呼页面
   */
  public showGreetingPage(): void {
    this.logger.info('显示打招呼页面', {
      isSDKReady: this.isReady,
      isInitialized: this.isInitialized,
    });

    // 确保SDK已完全就绪
    if (!this.isReady) {
      this.logger.warn('SDK未完全就绪，等待就绪后显示打招呼页面');
      this.onReady(() => {
        this.showGreetingPage();
      });
      return;
    }

    // 使用打招呼页面管理器显示页面
    this.greetingPageManager.show();
  }

  /**
   * 隐藏打招呼页面
   */
  public hideGreetingPage(): void {
    this.logger.info('隐藏打招呼页面');

    // 使用打招呼页面管理器隐藏页面
    this.greetingPageManager.hide();
  }

  /**
   * 获取打招呼页面管理器（用于高级页面控制）
   */
  public getGreetingPageManager(): GreetingPageManager {
    return this.greetingPageManager;
  }

  /**
   * 检查是否在打招呼页面
   */
  public isGreetingPageVisible(): boolean {
    return this.greetingPageManager.isGreetingPageVisible();
  }

  /**
   * 完全移除打招呼页面（更安全的清理方案）
   */
  public removeGreetingPage(): void {
    this.logger.info('完全移除打招呼页面');

    const greetingPage = document.querySelector('greeting-page') as Element & { hide?: () => void };
    if (greetingPage) {
      // 先隐藏页面
      if (greetingPage.hide) {
        greetingPage.hide();
      }

      // 等待动画完成后移除DOM元素
      setTimeout(() => {
        if (greetingPage.parentNode) {
          greetingPage.parentNode.removeChild(greetingPage);
          this.logger.info('✅ GreetingPage DOM元素已完全移除');
        }
      }, 300); // 给动画留出时间
    }
  }

  /**
   * 绑定SDK内部事件
   */
  private bindSDKEvents(): void {
    // 监听移除打招呼页面的请求
    this.eventBus.on('sdk:remove-greeting-page', () => {
      this.logger.info('收到移除打招呼页面请求');
      this.removeGreetingPage();
    });
  }

  /**
   * 获取数字人控制器
   */
  public getDigitalHumanController(): DigitalHumanController {
    return this.digitalHumanController;
  }

  /**
   * 销毁SDK
   */
  public destroy(): void {
    this.stop();

    // 销毁JSON-RPC管理器
    this.requestManager.destroy();
    this.actionHandler.destroy();
    this.notificationManager.destroy();

    // 销毁消息路由器
    this.messageRouter.destroy();

    // 销毁服务协调器
    this.serviceCoordinator.destroy();

    // 销毁服务客户端
    this.hksttClient.destroy();
    this.aiClient.destroy();

    // 销毁传输管理器
    this.transportManager.destroy();

    this.isInitialized = false;
    this.isReady = false; // 重置就绪状态
    this.logger.info('SDK已销毁');
  }

  // ============================================================================
  // 客户端JSON-RPC API
  // ============================================================================

  /**
   * 发送JSON-RPC请求并等待响应（增强版本，支持完整的JSON-RPC 2.0规范）
   * @param method 方法名
   * @param params 参数
   * @param options 请求选项
   * @returns Promise<响应结果>
   */
  public async sendRequest(
    method: string,
    params?: unknown,
    options: RequestOptions = {}
  ): Promise<unknown> {
    if (!this.isReady) {
      throw new Error('SDK未就绪，无法发送请求');
    }

    return this.requestManager.sendRequest(method, params, options);
  }

  /**
   * 清空所有待处理的请求（页面跳转或用户取消操作时使用）
   * 这是JSON-RPC文档中要求的核心功能，用于实现事件丢弃机制
   */
  public clearPendingRequests(): void {
    if (!this.isReady) {
      this.logger.warn('SDK未就绪，无法清空pending请求');
      return;
    }

    this.requestManager.clearAllPendingRequests();
  }

  /**
   * 获取当前待处理请求的状态信息（用于调试）
   */
  public getPendingRequestsInfo(): Array<{
    id: string;
    method: string;
    startTime: number;
    type: 'user' | 'chained' | 'server';
    parentId?: string;
  }> {
    if (!this.isReady) {
      return [];
    }

    return this.requestManager.getPendingRequests();
  }

  /**
   * 生成唯一的请求ID（高级用法）
   * @param prefix 可选的ID前缀
   * @returns 唯一的请求ID
   */
  public generateRequestId(prefix?: string): string {
    if (!this.isReady) {
      throw new Error('SDK未就绪，无法生成请求ID');
    }

    return this.requestManager.generateUniqueRequestId(prefix);
  }

  /**
   * 检查请求ID是否已存在（高级用法）
   * @param requestId 要检查的请求ID
   * @returns 是否存在
   */
  public hasRequestId(requestId: string): boolean {
    if (!this.isReady) {
      return false;
    }

    return this.requestManager.hasRequestId(requestId);
  }

  /**
   * 监听JSON-RPC通知
   * @param method 通知方法名
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  public onNotification(method: string, callback: NotificationCallback): () => void {
    return this.notificationManager.onNotification(method, callback);
  }

  /**
   * 监听AI响应中的Action（新增统一API）
   * @param callback 当收到包含action的响应时调用的回调函数
   * @returns 取消监听的函数
   */
  public onAction(callback: ActionCallback): () => void {
    return this.actionHandler.onAction(callback);
  }

  /**
   * 处理客户端JSON-RPC请求
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- JSON-RPC 请求格式多样
  private async handleClientRequest(request: any): Promise<void> {
    try {
      this.logger.info('📥 处理客户端JSON-RPC请求', {
        method: request.method,
        id: request.id,
        hasParams: request.params !== undefined,
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- JSON-RPC 结果类型多样
      let result: any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- JSON-RPC 错误类型多样
      let error: any = null;

      // 根据方法名处理不同的请求
      switch (request.method) {
        case 'speak':
          result = await this.handleSpeakRequest(request.params);
          break;

        case 'updateBackgroundInfo':
          result = await this.handleUpdateBackgroundInfoRequest(request.params);
          break;

        case 'addMessages':
          result = await this.handleAddMessagesRequest(request.params);
          break;

        case 'pushBizData':
          result = await this.handlePushBizDataRequest(request.params);
          break;

        default:
          error = this.jsonRpcHandler.createError(
            -32601,
            `方法未找到: ${request.method}`,
            request.id
          );
          break;
      }

      // 发送响应
      if (error) {
        this.eventBus.emit('jsonrpc:response', error);
      } else {
        const response = this.jsonRpcHandler.createResponse(result, request.id);
        this.eventBus.emit('jsonrpc:response', response);
      }
    } catch (err) {
      this.logger.error('处理客户端请求失败', { error: err, request });

      const errorResponse = this.jsonRpcHandler.createError(-32603, '内部错误', request.id, {
        message: err instanceof Error ? err.message : String(err),
      });

      this.eventBus.emit('jsonrpc:response', errorResponse);
    }
  }

  /**
   * 处理speak请求
   */
  private async handleSpeakRequest(
    params: SpeakRequestParams
  ): Promise<{ success: boolean; message?: string }> {
    const { text, delay = 0, display = true } = params;

    this.logger.info('处理speak请求', { text: text?.substring(0, 50), delay, display });

    // TODO: 实现TTS播报逻辑
    // 这里应该调用TTS服务

    return { success: true, message: '播报请求已处理' };
  }

  /**
   * 处理updateBackgroundInfo请求
   */
  private async handleUpdateBackgroundInfoRequest(
    params: UpdateBackgroundInfoParams
  ): Promise<{ sessionId: string }> {
    const { sessionId, page, status } = params;

    this.logger.info('处理updateBackgroundInfo请求', { sessionId, page, status });

    // 发送背景信息更新事件
    this.eventBus.emit('background:info-updated', { sessionId, page, status });

    return { sessionId: sessionId || 'default-session' };
  }

  /**
   * 处理addMessages请求
   */
  private async handleAddMessagesRequest(
    params: AddMessagesParams
  ): Promise<{ sessionId: string }> {
    const { sessionId, messages } = params;

    this.logger.info('处理addMessages请求', { sessionId, messageCount: messages?.length });

    // 发送消息添加事件
    this.eventBus.emit('messages:added', { sessionId, messages });

    return { sessionId: sessionId || 'default-session' };
  }

  /**
   * 处理pushBizData请求
   */
  private async handlePushBizDataRequest(params: PushBizDataParams): Promise<{ success: boolean }> {
    const { key, data } = params;

    this.logger.info('处理pushBizData请求', { key, hasData: data !== undefined });

    // 发送业务数据推送事件
    this.eventBus.emit('biz:data-pushed', { key, data });

    return { success: true };
  }

  // ============================================================================
  // 增强的JSON-RPC请求-响应处理（已模块化）
  // ============================================================================

  // ============================================================================
  // 模块化处理已完成，所有JSON-RPC逻辑已移至专门的管理器
  // ============================================================================
}

/**
 * 初始化并自动启动SDK - 唯一的初始化入口
 */
export async function init(config: WebSDKConfig): Promise<WebSDK> {
  const sdk = WebSDK.getInstance(config);
  await sdk._internalStart();
  return sdk;
}

/**
 * 获取SDK实例
 */
export function getWebSDK(): WebSDK {
  const instance = WebSDK.getInstance();
  if (!instance) {
    throw ErrorHandler.createSystemError('SDK未初始化，请先调用initializeWebSDK');
  }
  return instance;
}
