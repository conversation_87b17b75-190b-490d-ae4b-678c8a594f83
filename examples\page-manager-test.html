<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轻量级页面管理器测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .test-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .test-btn.primary {
            background: #4a90e2;
            color: white;
        }

        .test-btn.primary:hover {
            background: #357abd;
            transform: translateY(-2px);
        }

        .test-btn.secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .test-btn.secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-box h3 {
            margin-top: 0;
            color: #333;
        }

        .info-box ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .info-box li {
            margin: 5px 0;
            color: #666;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        #logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }

        .log-entry.info {
            background: #e3f2fd;
            color: #1565c0;
        }

        .log-entry.success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .log-entry.error {
            background: #ffebee;
            color: #c62828;
        }

        .log-entry.warn {
            background: #fff3e0;
            color: #ef6c00;
        }

        /* 主页面内容样式 */
        .main-content {
            background: #e3f2fd;
            padding: 40px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: center;
        }

        .main-content h2 {
            color: #1565c0;
            margin-bottom: 20px;
        }

        .main-content p {
            color: #424242;
            font-size: 16px;
            line-height: 1.6;
        }

        /* ChatWidget 样式 */
        chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 轻量级页面管理器测试</h1>
            <p>测试新的页面级架构和打招呼页面功能</p>
        </div>

        <div class="info-box">
            <h3>📋 测试功能清单</h3>
            <ul>
                <li>✅ 页面级显示/隐藏</li>
                <li>✅ 状态隔离和管理</li>
                <li>✅ 无缝页面切换</li>
                <li>✅ 事件系统集成</li>
                <li>✅ ASR和AI服务隔离</li>
                <li>✅ 会话状态管理</li>
            </ul>
        </div>

        <div class="test-controls">
            <button class="test-btn primary" onclick="showGreetingPage()">显示打招呼页面</button>
            <button class="test-btn secondary" onclick="hideGreetingPage()">隐藏打招呼页面</button>
            <button class="test-btn secondary" onclick="checkPageState()">检查页面状态</button>
            <button class="test-btn secondary" onclick="testPageManager()">测试页面管理器</button>
            <button class="test-btn secondary" onclick="clearLogs()">清空日志</button>
        </div>

        <div id="status" class="status info">
            正在初始化SDK...
        </div>

        <div id="logs">
            <div class="log-entry info">等待日志输出...</div>
        </div>
    </div>

    <!-- 主页面内容 -->
    <div class="main-content">
        <h2>🏠 主页面内容</h2>
        <p>这是主页面的内容。当显示打招呼页面时，这些内容会被隐藏。</p>
        <p>页面管理器会自动处理页面间的切换，确保状态正确隔离。</p>
    </div>

    <!-- ChatWidget 组件 -->
    <chat-widget></chat-widget>

    <!-- 打招呼页面组件 -->
    <greeting-page title="欢迎使用数字人服务" welcome-message="您好！我是您的智能助手，很高兴为您服务。" show-close-button="true">
    </greeting-page>

    <!-- 加载SDK -->
    <script src="../dist/web-service-sdk.js"></script>
    <script>
        let sdk = null;
        let pageManager = null;

        // 日志函数
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(entry);
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // 测试函数
        function showGreetingPage() {
            if (sdk) {
                sdk.showGreetingPage();
                addLog('调用 sdk.showGreetingPage()', 'success');
            } else {
                addLog('SDK 未初始化', 'error');
            }
        }

        function hideGreetingPage() {
            if (sdk) {
                sdk.hideGreetingPage();
                addLog('调用 sdk.hideGreetingPage()', 'success');
            } else {
                addLog('SDK 未初始化', 'error');
            }
        }

        function checkPageState() {
            if (pageManager) {
                const state = pageManager.getPageState();
                addLog(`页面状态: ${JSON.stringify(state, null, 2)}`, 'info');

                const isGreetingVisible = sdk.isGreetingPageVisible();
                addLog(`打招呼页面可见: ${isGreetingVisible}`, 'info');
            } else {
                addLog('页面管理器未初始化', 'error');
            }
        }

        function testPageManager() {
            if (pageManager) {
                addLog('测试页面管理器功能:', 'info');
                addLog(`- 当前页面: ${pageManager.getCurrentPage()}`, 'info');
                addLog(`- 打招呼页面可见: ${pageManager.isGreetingPageVisible()}`, 'info');

                // 测试页面状态
                const state = pageManager.getPageState();
                addLog(`- 页面状态: ${JSON.stringify(state)}`, 'info');
            } else {
                addLog('页面管理器未找到', 'error');
            }
        }

        function clearLogs() {
            const logs = document.getElementById('logs');
            logs.innerHTML = '<div class="log-entry info">日志已清空</div>';
        }

        // 初始化
        addLog('开始初始化SDK...');

        WebServiceSDK.init({
            hksttUrl: 'ws://*************:20096',
            aiServerUrl: 'http://localhost:8002',
            ttsUrl: 'http://*************:8080', // TTS服务地址（可选，启用语音播放）
            debug: false
        }).then(sdkInstance => {
            sdk = sdkInstance;
            window.sdk = sdk;

            addLog('SDK初始化成功', 'success');
            updateStatus('SDK已连接，页面管理器就绪！', 'success');

            // 获取页面管理器
            pageManager = sdk.getPageManager();
            window.pageManager = pageManager;

            addLog('页面管理器已获取', 'success');

            // 监听页面管理器事件
            sdk.getEventBus().on('page:show-greeting', (data) => {
                addLog(`页面管理器事件: 显示打招呼页面 ${JSON.stringify(data)}`, 'info');
            });

            sdk.getEventBus().on('page:hide-greeting', () => {
                addLog('页面管理器事件: 隐藏打招呼页面', 'info');
            });

            // 监听打招呼页面组件事件
            const greetingPage = document.querySelector('greeting-page');
            if (greetingPage) {
                greetingPage.addEventListener('show', (event) => {
                    addLog(`打招呼页面显示事件: ${JSON.stringify(event.detail)}`, 'info');
                });

                greetingPage.addEventListener('hide', (event) => {
                    addLog(`打招呼页面隐藏事件: ${JSON.stringify(event.detail)}`, 'info');
                });

                greetingPage.addEventListener('back', () => {
                    addLog('用户点击返回按钮', 'info');
                });
            }

            // 5秒后自动显示打招呼页面
            setTimeout(() => {
                showGreetingPage();
                addLog('自动显示打招呼页面（5秒后）', 'info');
            }, 5000);

        }).catch(error => {
            addLog(`SDK初始化失败: ${error.message}`, 'error');
            updateStatus('SDK初始化失败', 'error');
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            addLog('页面加载完成');
        });
    </script>
</body>

</html>