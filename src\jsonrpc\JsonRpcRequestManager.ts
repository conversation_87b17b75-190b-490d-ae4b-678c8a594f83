/**
 * JSON-RPC 请求管理器
 * 负责管理待处理的请求和响应匹配
 */

import { nanoid } from 'nanoid';

import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

import { JsonRpcValidator } from './JsonRpcValidator';

/**
 * 待处理请求信息
 */
interface PendingRequest {
  id: string;
  method: string;
  resolve: (result: unknown) => void;
  reject: (error: Error) => void;
  timeoutId: NodeJS.Timeout;
  startTime: number;
  /** 请求类型：user=用户主动发送，chained=链式响应，server=服务端请求 */
  type: 'user' | 'chained' | 'server';
  /** 父请求ID（用于链式响应） */
  parentId?: string;
}

/**
 * 请求选项
 */
export interface RequestOptions {
  /** 超时时间（毫秒），默认30秒 */
  timeout?: number;
  /** 自定义请求ID，如果不提供则自动生成 */
  id?: string | number;
}

/**
 * JSON-RPC 请求管理器
 */
export class JsonRpcRequestManager {
  private eventBus: EventBus;
  private logger: Logger;
  private validator: JsonRpcValidator;
  private pendingRequests = new Map<string, PendingRequest>();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcRequestManager' });
    this.validator = new JsonRpcValidator();

    this.setupEventListeners();
  }

  /**
   * 发送JSON-RPC请求
   */
  public async sendRequest(
    method: string,
    params?: unknown,
    options: RequestOptions = {}
  ): Promise<unknown> {
    const { timeout = 30000, id: customId } = options;

    // 验证方法名
    if (!this.validator.validateMethodName(method)) {
      throw new Error(`无效的方法名: ${method}`);
    }

    // 验证参数
    if (!this.validator.validateMethodParams(method, params)) {
      throw new Error(`方法 ${method} 的参数格式不正确`);
    }

    // 生成或使用自定义请求ID
    const requestId = customId ? String(customId) : nanoid();

    // 检查ID冲突（如果使用自定义ID）
    if (customId && this.pendingRequests.has(requestId)) {
      throw new Error(`请求ID冲突: ${requestId} 已存在于pending列表中`);
    }

    // 创建请求对象
    const request = {
      jsonrpc: '2.0' as const,
      method,
      params,
      id: requestId,
    };

    // 验证请求格式
    if (!this.validator.validateRequest(request)) {
      throw new Error('请求格式不符合JSON-RPC 2.0规范');
    }

    this.logger.info('📤 发送JSON-RPC请求', {
      method,
      id: requestId,
      isCustomId: !!customId,
      hasParams: params !== undefined,
      timeout,
    });

    return new Promise((resolve, reject) => {
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        this.handleRequestTimeout(requestId);
      }, timeout);

      // 创建待处理请求记录
      const pendingRequest: PendingRequest = {
        id: requestId,
        method,
        resolve: resolve as (result: unknown) => void,
        reject,
        timeoutId,
        startTime: Date.now(),
        type: 'user', // 用户主动发送的请求
      };

      // 添加到待处理请求集合
      this.pendingRequests.set(requestId, pendingRequest);

      // 发送请求事件
      this.eventBus.emit('client:send-request', request);
    });
  }

  /**
   * 取消指定的请求
   */
  public cancelRequest(requestId: string): boolean {
    const pendingRequest = this.pendingRequests.get(requestId);
    if (!pendingRequest) {
      return false;
    }

    this.logger.info('🚫 取消JSON-RPC请求', { requestId });

    // 清理超时定时器
    clearTimeout(pendingRequest.timeoutId);

    // 拒绝Promise
    pendingRequest.reject(new Error('请求已取消'));

    // 从待处理集合中移除
    this.pendingRequests.delete(requestId);

    return true;
  }

  /**
   * 取消所有待处理请求
   */
  public cancelAllRequests(): void {
    this.logger.info('🚫 取消所有待处理请求', { count: this.pendingRequests.size });

    for (const [requestId] of this.pendingRequests) {
      this.cancelRequest(requestId);
    }
  }

  /**
   * 注册链式响应ID（当收到包含nextRequestId的响应时调用）
   */
  public registerChainedRequest(
    chainedId: string,
    parentId: string,
    timeout: number = 30000
  ): void {
    // 检查父请求是否存在（可能已经完成）
    const parentRequest = this.pendingRequests.get(parentId);
    const parentMethod = parentRequest?.method || 'unknown';

    this.logger.info('🔗 注册链式响应ID', {
      chainedId,
      parentId,
      parentMethod,
      timeout,
    });

    return new Promise<unknown>((resolve, reject) => {
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        this.handleRequestTimeout(chainedId);
      }, timeout);

      // 创建链式请求记录
      const chainedRequest: PendingRequest = {
        id: chainedId,
        method: `${parentMethod}:chained`, // 标记为链式请求
        resolve: resolve as (result: unknown) => void,
        reject,
        timeoutId,
        startTime: Date.now(),
        type: 'chained',
        parentId,
      };

      // 添加到待处理请求集合
      this.pendingRequests.set(chainedId, chainedRequest);
    }) as any; // 返回void，但内部创建Promise用于管理生命周期
  }

  /**
   * 获取待处理请求数量
   */
  public getPendingRequestCount(): number {
    return this.pendingRequests.size;
  }

  /**
   * 获取待处理请求列表
   */
  public getPendingRequests(): Array<{
    id: string;
    method: string;
    startTime: number;
    type: 'user' | 'chained' | 'server';
    parentId?: string;
  }> {
    return Array.from(this.pendingRequests.values()).map(req => {
      const result: any = {
        id: req.id,
        method: req.method,
        startTime: req.startTime,
        type: req.type,
      };
      if (req.parentId) {
        result.parentId = req.parentId;
      }
      return result;
    });
  }

  /**
   * 取消指定请求及其所有链式子请求
   */
  public cancelRequestChain(requestId: string): number {
    let cancelledCount = 0;

    // 取消主请求
    if (this.cancelRequest(requestId)) {
      cancelledCount++;
    }

    // 查找并取消所有子链式请求
    const childRequests = Array.from(this.pendingRequests.values()).filter(
      req => req.parentId === requestId
    );

    for (const childRequest of childRequests) {
      // 递归取消子请求的链式请求
      cancelledCount += this.cancelRequestChain(childRequest.id);
    }

    if (cancelledCount > 1) {
      this.logger.info('🚫 取消请求链', { rootId: requestId, cancelledCount });
    }

    return cancelledCount;
  }

  /**
   * 清空所有pending请求（页面跳转/用户取消时使用）
   * 这是文档中要求的核心功能
   */
  public clearAllPendingRequests(): void {
    const count = this.pendingRequests.size;
    if (count === 0) return;

    this.logger.info('🧹 清空所有pending请求', {
      count,
      reason: '页面跳转或用户取消操作',
    });

    // 取消所有请求（包括链式请求）
    this.cancelAllRequests();
  }

  /**
   * 检查请求ID是否已存在
   */
  public hasRequestId(requestId: string): boolean {
    return this.pendingRequests.has(requestId);
  }

  /**
   * 生成唯一的请求ID（确保不与现有ID冲突）
   */
  public generateUniqueRequestId(prefix?: string): string {
    let requestId: string;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      if (prefix) {
        requestId = `${prefix}_${nanoid(8)}`;
      } else {
        requestId = nanoid();
      }
      attempts++;

      if (attempts > maxAttempts) {
        throw new Error('无法生成唯一的请求ID，请检查pending请求数量');
      }
    } while (this.pendingRequests.has(requestId));

    return requestId;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.logger.info('🗑️ 销毁JSON-RPC请求管理器');

    // 取消所有待处理请求
    this.cancelAllRequests();

    // 清理事件监听器
    this.eventBus.off('jsonrpc:response', this.handleResponse);
    this.eventBus.off('jsonrpc:error-response', this.handleResponse);

    this.logger.info('✅ JSON-RPC请求管理器已销毁');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听响应事件
    this.eventBus.on('jsonrpc:response', this.handleResponse);
    this.eventBus.on('jsonrpc:error-response', this.handleResponse);
  }

  /**
   * 处理收到的JSON-RPC响应
   */
  private handleResponse = (response: unknown): boolean => {
    // 验证响应格式
    if (!this.validator.validateResponse(response)) {
      this.logger.warn('收到无效的JSON-RPC响应', { response });
      return false;
    }

    const resp = response as any;
    const requestId = String(resp.id);
    const pendingRequest = this.pendingRequests.get(requestId);

    if (!pendingRequest) {
      this.logger.warn('收到未知请求的响应，可能已超时或取消', { responseId: requestId });
      return false;
    }

    this.logger.info('📥 收到JSON-RPC响应', {
      id: requestId,
      method: pendingRequest.method,
      isError: 'error' in resp,
      responseTime: Date.now() - pendingRequest.startTime,
    });

    // 清理超时定时器
    clearTimeout(pendingRequest.timeoutId);

    // 从待处理集合中移除
    this.pendingRequests.delete(requestId);

    // 处理响应
    if ('error' in resp) {
      // 错误响应
      const error = new Error(`JSON-RPC错误 [${resp.error.code}]: ${resp.error.message}`);
      (error as any).code = resp.error.code;
      (error as any).data = resp.error.data;
      pendingRequest.reject(error);
    } else {
      // 成功响应
      const result = resp.result;

      // 检查是否包含nextRequestId（链式响应）
      if (result && typeof result === 'object' && result.nextRequestId) {
        this.logger.info('🔗 检测到链式响应', {
          currentId: requestId,
          nextId: result.nextRequestId,
        });

        // 注册下一个链式响应ID
        this.registerChainedRequest(
          result.nextRequestId as string,
          requestId,
          30000 // 使用默认超时
        );

        // 发送链式响应事件（保持现有行为）
        this.eventBus.emit('ai:chained-request', {
          requestId: result.nextRequestId,
          parentId: requestId,
          timestamp: Date.now(),
        });
      }

      pendingRequest.resolve(result);
    }

    return true;
  };

  /**
   * 处理请求超时
   */
  private handleRequestTimeout(requestId: string): void {
    const pendingRequest = this.pendingRequests.get(requestId);
    if (!pendingRequest) {
      return;
    }

    this.logger.warn('⏰ JSON-RPC请求超时', {
      id: requestId,
      method: pendingRequest.method,
      elapsedTime: Date.now() - pendingRequest.startTime,
    });

    // 从待处理集合中移除
    this.pendingRequests.delete(requestId);

    // 拒绝Promise
    pendingRequest.reject(new Error(`请求超时 (${Date.now() - pendingRequest.startTime}ms)`));
  }
}
