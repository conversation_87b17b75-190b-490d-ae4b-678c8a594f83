/**
 * 会话管理器
 * 管理数字人对话会话
 */

// 简单的会话和消息类型
interface SessionData {
  id: string;
  createdAt: number;
  messages: Message[];
}

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}
// 简单的ID生成函数
function generateSessionId(): string {
  return `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 会话管理器类
 */
export class SessionManager {
  private sessions: Map<string, SessionData> = new Map();
  private currentSessionId: string | null = null;

  constructor() {
    // 创建默认会话
    this.createSession();
  }

  /**
   * 创建新会话
   * @returns 会话ID
   */
  public createSession(): string {
    const sessionId = generateSessionId();

    this.sessions.set(sessionId, {
      id: sessionId,
      createdAt: Date.now(),
      messages: [],
    });

    this.currentSessionId = sessionId;

    return sessionId;
  }

  /**
   * 获取会话
   * @param sessionId 会话ID，如果不提供则返回当前会话
   * @returns 会话数据或null
   */
  public getSession(sessionId?: string): SessionData | null {
    const id = sessionId || this.currentSessionId;

    if (id && this.sessions.has(id)) {
      return this.sessions.get(id) || null;
    }

    return null;
  }

  /**
   * 获取当前会话ID
   * @returns 当前会话ID
   */
  public getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * 设置当前会话
   * @param sessionId 会话ID
   */
  public setCurrentSession(sessionId: string): void {
    if (this.sessions.has(sessionId)) {
      this.currentSessionId = sessionId;
    }
  }

  /**
   * 添加消息到会话
   * @param message 消息
   * @param sessionId 会话ID，如果不提供则添加到当前会话
   */
  public addMessage(message: Message, sessionId?: string): void {
    const session = this.getSession(sessionId);

    if (session) {
      session.messages.push(message);
    }
  }

  /**
   * 获取会话消息
   * @param sessionId 会话ID，如果不提供则返回当前会话消息
   * @returns 消息列表
   */
  public getMessages(sessionId?: string): Message[] {
    const session = this.getSession(sessionId);
    return session ? session.messages : [];
  }

  /**
   * 清空会话消息
   * @param sessionId 会话ID，如果不提供则清空当前会话
   */
  public clearMessages(sessionId?: string): void {
    const session = this.getSession(sessionId);

    if (session) {
      session.messages = [];
    }
  }

  /**
   * 删除会话
   * @param sessionId 会话ID
   */
  public deleteSession(sessionId: string): void {
    this.sessions.delete(sessionId);

    // 如果删除的是当前会话，创建新的默认会话
    if (this.currentSessionId === sessionId) {
      this.createSession();
    }
  }

  /**
   * 获取所有会话ID
   * @returns 会话ID列表
   */
  public getAllSessionIds(): string[] {
    return Array.from(this.sessions.keys());
  }

  /**
   * 获取会话数量
   * @returns 会话数量
   */
  public getSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * 清空所有会话
   */
  public clearAllSessions(): void {
    this.sessions.clear();
    this.currentSessionId = null;
    // 创建新的默认会话
    this.createSession();
  }
}
