/**
 * ChatWidget Web Components包装器
 * 提供与原Lit版本完全兼容的API，内部使用React实现
 */

import { WebSDK } from '../core/WebSDK';
import { Logger } from '../utils/Logger';

import { ChatWidgetManager } from './ChatWidgetManager';

/**
 * React版本的ChatWidget Web Component包装器
 */
export class ChatWidgetWrapper extends HTMLElement {
  private logger: Logger;
  private manager: ChatWidgetManager | null = null;
  private sdk: WebSDK | null = null;
  private isStarted = false;

  // 监听的属性列表（与Lit版本保持一致）
  static get observedAttributes() {
    return ['theme', 'max-messages', 'enable-voice', 'avatar-url', 'default-voice'];
  }

  constructor() {
    super();
    this.logger = Logger.getInstance({ prefix: 'ChatWidgetWrapper' });

    // 设置固定尺寸（与Lit版本一致）
    this.style.display = 'block';
    this.style.width = '420px';
    this.style.height = '880px';
  }

  /**
   * 元素连接到DOM时调用
   */
  connectedCallback(): void {
    this.logger.info('ChatWidgetWrapper已连接到DOM');

    // 注册到全局组件管理器，等待SDK初始化完成后通知
    this.registerToSDK();
  }

  /**
   * 元素从DOM断开时调用
   */
  disconnectedCallback(): void {
    this.logger.info('ChatWidgetWrapper已从DOM断开');
    this.cleanup();
  }

  /**
   * 属性变化时调用
   */
  attributeChangedCallback(name: string, oldValue: string | null, newValue: string | null): void {
    if (oldValue === newValue) return;

    this.logger.info('属性变化', { name, oldValue, newValue });

    // 如果管理器已创建，更新属性
    if (this.manager) {
      this.updateManagerProps();
    }
  }

  /**
   * 注册到SDK组件管理器（与Lit版本完全一致）
   */
  private registerToSDK(): void {
    // 在window上注册组件，等待SDK初始化完成后调用
    if (!window.webSDKComponents) {
      window.webSDKComponents = [];
    }
    window.webSDKComponents.push({
      type: 'chat-widget',
      instance: this,
      initialize: (sdk: WebSDK) => this.initializeWithSDK(sdk),
    });

    this.logger.info('ChatWidgetWrapper已注册，等待SDK初始化');
  }

  /**
   * 由SDK调用，传入SDK实例进行初始化（与Lit版本完全一致）
   */
  public initializeWithSDK(sdk: WebSDK): void {
    this.sdk = sdk;
    this.logger.info('ChatWidgetWrapper已接收到SDK实例');

    // 创建管理器
    this.manager = new ChatWidgetManager(sdk, this);

    // 设置初始属性
    this.updateManagerProps();

    this.logger.info('ChatWidgetWrapper已成功初始化');

    // 【关键】自动启动，与Lit版本保持一致
    if (sdk && sdk.isSDKReady()) {
      this.start();
    } else {
      this.logger.warn('SDK未就绪，使用延迟启动机制');
      this.delayedStart();
    }
  }

  /**
   * 启动ChatWidget（与Lit版本API保持一致）
   */
  public async start(): Promise<void> {
    try {
      // 防止重复启动
      if (this.isStarted) {
        this.logger.warn('ChatWidget已启动，跳过重复启动');
        return;
      }

      this.logger.info('启动ChatWidget');

      if (!this.sdk) {
        this.logger.warn('SDK未就绪，无法启动ChatWidget');
        return;
      }

      if (!this.manager) {
        this.logger.warn('管理器未创建，无法启动ChatWidget');
        return;
      }

      // 标记为已启动
      this.isStarted = true;

      // 显示React组件
      this.manager.show();

      this.logger.info('ChatWidget启动成功');
    } catch (error) {
      this.logger.error('启动ChatWidget失败', error);
      this.isStarted = false;
    }
  }

  /**
   * 停止ChatWidget
   */
  public stop(): void {
    if (!this.isStarted) {
      this.logger.warn('ChatWidget未启动，跳过停止操作');
      return;
    }

    this.logger.info('停止ChatWidget');

    if (this.manager) {
      this.manager.hide();
    }

    this.isStarted = false;
    this.logger.info('ChatWidget已停止');
  }

  /**
   * 延迟启动ChatWidget，确保SDK就绪（与Lit版本保持一致）
   */
  private delayedStart(): void {
    // 检查SDK是否存在且就绪
    if (!this.sdk) {
      // 等待SDK初始化
      setTimeout(() => this.delayedStart(), 100);
      return;
    }

    if (!this.sdk.isSDKReady()) {
      this.logger.warn('SDK未就绪，等待SDK完全启动...');
      // 等待SDK就绪
      setTimeout(() => this.delayedStart(), 100);
      return;
    }

    // SDK就绪，启动ChatWidget
    this.start();
  }

  /**
   * 更新管理器属性
   */
  private updateManagerProps(): void {
    if (!this.manager) return;

    const theme = (this.getAttribute('theme') as 'light' | 'dark') || 'light';
    const maxMessages = parseInt(this.getAttribute('max-messages') || '100', 10);
    const enableVoiceInput = this.getAttribute('enable-voice') !== 'false';
    const avatarUrl = this.getAttribute('avatar-url') || '';
    const defaultVoice = this.getAttribute('default-voice') || 'mandarin';

    this.manager.setProps({
      theme,
      maxMessages,
      enableVoiceInput,
      avatarUrl,
      defaultVoice,
    });
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    try {
      if (this.manager) {
        this.manager.destroy();
        this.manager = null;
      }
      this.isStarted = false;
      this.logger.info('ChatWidgetWrapper资源已清理');
    } catch (error) {
      this.logger.error('清理ChatWidgetWrapper资源失败', error);
    }
  }

  /**
   * 获取当前状态（兼容性API）
   */
  public getStatus(): { isStarted: boolean; isVisible: boolean } {
    return {
      isStarted: this.isStarted,
      isVisible: this.manager?.isShowing() || false,
    };
  }
}

// 注册自定义元素（直接替换原来的chat-widget标签）
if (!customElements.get('chat-widget')) {
  customElements.define('chat-widget', ChatWidgetWrapper);
}
