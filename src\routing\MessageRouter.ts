/**
 * 消息路由器 - 专注核心路由功能
 * 负责将ASR结果和AI响应路由到正确的目标
 */

import { EventBus } from '../core/EventBus';
import {
  ScenarioType,
  MessageTarget,
  RoutingState,
  RoutingDecision,
  RoutingContext,
  RoutingChangeEvent,
  MessageRouteEvent,
} from '../types/routing';
import { generateTypedRequestId, generateUUID } from '../utils/helpers';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * 消息路由器配置
 */
export interface MessageRouterConfig {
  /** 默认场景 */
  defaultScenario: ScenarioType;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 消息路由器
 */
export class MessageRouter {
  private eventBus: EventBus;
  private logger: Logger;
  private config: Required<MessageRouterConfig>;

  // 当前路由状态
  private routingState: RoutingState;

  // 路由上下文
  private routingContext: RoutingContext = {
    hasCustomComponent: false,
    isGreetingPageVisible: false,
  };

  // 组件专用会话ID映射（现在直接存储UUID）
  private componentSessionIds: Map<MessageTarget, string> = new Map();

  // UUID到组件的映射（用于路由决策）
  private sessionIdToComponentMap: Map<string, MessageTarget> = new Map();

  constructor(config: MessageRouterConfig, eventBus: EventBus) {
    this.config = {
      debug: false,
      ...config,
    };

    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'MessageRouter',
    });

    // 初始化路由状态
    this.routingState = {
      currentScenario: this.config.defaultScenario,
      sessionId: this.generateSessionId(),
      isActive: true,
    };

    this.bindEvents();
    this.logger.info('消息路由器已初始化', {
      defaultScenario: this.config.defaultScenario,
    });
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听ASR识别结果
    this.eventBus.on('hkstt:asr-offline-result', (data: any) => {
      this.handleASRResult(data);
    });

    // 只监听ServiceCoordinator处理后的AI最终响应，避免重复处理
    this.eventBus.on('ai:chat-final-response', (data: any) => {
      this.handleAIResponse(data);
    });

    // 监听ServiceCoordinator处理后的AI响应（用于非流式响应）
    this.eventBus.on('ai:chat-response-processed', (data: any) => {
      this.handleAIResponse(data);
    });

    // 拦截所有AI请求，统一处理会话ID转换
    this.eventBus.on('ai:send-chat-request', (data: any) => {
      this.logger.info('🔍 MessageRouter拦截到AI请求', {
        sessionId: data.sessionId,
        userInput: data.userInput?.substring(0, 30) + '...',
        requestId: data.requestId,
      });
      this.interceptAIRequest(data);
    });

    // 监听场景切换请求
    this.eventBus.on('router:switch-scenario', (data: any) => {
      this.logger.info('🔄 收到场景切换请求', data);
      this.switchScenario(data.scenario, data.sessionId);
    });

    // 监听组件状态变化
    this.eventBus.on('component:custom-component-ready', (data: any) => {
      this.logger.info('📢 收到自定义组件就绪通知', data);
      this.updateRoutingContext({ hasCustomComponent: true });
      this.logRoutingState('自定义组件就绪后');
    });

    this.eventBus.on('component:greeting-page-visible', (data: any) => {
      this.logger.info('📢 收到打招呼页面显示通知', {
        data,
        previousState: this.routingContext.isGreetingPageVisible,
      });

      // 记录组件的会话ID，避免创建新的
      if (data?.sessionId) {
        this.componentSessionIds.set('greeting-page', data.sessionId);
        this.sessionIdToComponentMap.set(data.sessionId, 'greeting-page');
        this.logger.info('📝 记录打招呼页面会话ID', {
          sessionId: data.sessionId,
        });
      }

      this.updateRoutingContext({ isGreetingPageVisible: true });
      this.logger.info('✅ 打招呼页面状态已更新为可见', {
        isGreetingPageVisible: this.routingContext.isGreetingPageVisible,
      });
      this.logRoutingState('打招呼页面显示后');
    });

    this.eventBus.on('component:greeting-page-hidden', (data: any) => {
      this.logger.info('📢 收到打招呼页面隐藏通知', {
        data,
        previousState: this.routingContext.isGreetingPageVisible,
      });
      this.updateRoutingContext({ isGreetingPageVisible: false });
      this.logger.info('✅ 打招呼页面状态已更新为隐藏', {
        isGreetingPageVisible: this.routingContext.isGreetingPageVisible,
      });
      this.logRoutingState('打招呼页面隐藏后');
    });

    // 监听打招呼页面返回事件
    this.eventBus.on('greeting-page:back', (data: any) => {
      this.logger.info('📢 收到打招呼页面返回事件', {
        data,
        currentScenario: this.routingState.currentScenario,
        routingContext: this.routingContext,
      });

      // 重要：先更新打招呼页面状态为不可见
      this.updateRoutingContext({ isGreetingPageVisible: false });
      this.logger.info('✅ 打招呼页面状态已更新为不可见（返回事件）', {
        isGreetingPageVisible: this.routingContext.isGreetingPageVisible,
      });

      // 清理打招呼页面的会话ID
      this.clearComponentSession('greeting-page');

      // 切换回自定义组件场景
      this.logger.info('🔄 用户从打招呼页面返回，切换到自定义组件场景');
      this.switchScenario('custom-component', data.sessionId);

      // 通知SDK完全移除打招呼页面（更安全的清理）
      this.eventBus.emit('sdk:remove-greeting-page');

      this.logger.info('✅ 已处理打招呼页面返回事件，场景已切换到custom-component');
      this.logRoutingState('处理返回事件后');
    });

    // 监听组件隐藏事件，清理对应的会话ID
    this.eventBus.on('component:greeting-page-hidden', (data: any) => {
      this.logger.info('📢 收到打招呼页面隐藏通知', data);
      this.clearComponentSession('greeting-page');
    });

    this.eventBus.on('component:custom-component-hidden', (data: any) => {
      this.logger.info('📢 收到自定义组件隐藏通知', data);
      this.clearComponentSession('custom-component');
    });
  }

  /**
   * 处理ASR识别结果
   */
  private handleASRResult(data: { sid: string; text: string }): void {
    const { text, sid } = data;

    this.logger.info('🎤 收到ASR识别结果', {
      text: text.substring(0, 50) + '...',
      sid,
      currentScenario: this.routingState.currentScenario,
      routingContext: this.routingContext,
    });

    // ASR路由决策：基于页面可见性进行智能路由
    const decision = this.makeASRRoutingDecision();

    this.logger.info('🎯 ASR路由决策结果', {
      target: decision.target,
      sessionId: decision.sessionId,
      reason: decision.reason,
    });

    // 路由消息
    this.routeMessage({
      messageType: 'asr-result',
      target: decision.target,
      sessionId: decision.sessionId,
      content: text,
    });

    // 发送用户输入事件到目标（使用内部会话ID）
    const eventName = `${decision.target}:user-input`;
    const requestId = generateTypedRequestId('asr');
    const internalSessionId = decision.sessionId;
    const eventData = {
      userInput: text,
      sessionId: internalSessionId,
      requestId,
    };

    this.logger.info('📤 发送ASR用户输入事件', {
      eventName,
      eventData,
    });

    this.eventBus.emit(eventName, eventData);

    // 直接使用UUID会话ID（无需转换）
    this.logger.info('🤖 发送AI聊天请求（ASR触发）', {
      sessionId: internalSessionId,
      userInput: text,
      requestId,
    });

    // 直接发送到ServiceCoordinator，跳过拦截器（避免重复处理）
    this.eventBus.emit('ai:send-chat-request-internal', {
      userInput: text,
      sessionId: internalSessionId, // 直接使用UUID
      requestId,
    });
  }

  /**
   * 拦截AI请求，统一处理会话ID（现在直接使用UUID）
   */
  private interceptAIRequest(data: {
    userInput: string;
    sessionId: string;
    requestId?: string;
  }): void {
    const { userInput, sessionId, requestId } = data;

    this.logger.info('🔄 拦截AI请求（纯UUID模式）', {
      sessionId,
      userInput: userInput.substring(0, 50) + '...',
      requestId,
    });

    // 检查是否是旧格式的会话ID（带前缀），如果是则转换为UUID
    let finalSessionId: string;
    const isLegacySessionId =
      sessionId.startsWith('custom-') ||
      sessionId.startsWith('greeting-') ||
      sessionId.includes('_custom_') ||
      sessionId.includes('_greeting_') ||
      sessionId.startsWith('session_') ||
      sessionId.includes('greeting_session_');

    if (isLegacySessionId) {
      // 旧格式会话ID，生成新的UUID并建立映射
      finalSessionId = generateUUID();

      // 从旧会话ID推断组件类型
      let targetComponent: MessageTarget;
      if (sessionId.includes('custom') || sessionId.includes('chat')) {
        targetComponent = 'custom-component';
      } else {
        targetComponent = 'greeting-page';
      }

      // 建立映射
      this.componentSessionIds.set(targetComponent, finalSessionId);
      this.sessionIdToComponentMap.set(finalSessionId, targetComponent);

      this.logger.info('🔄 转换旧格式会话ID为UUID', {
        oldSessionId: sessionId,
        newSessionId: finalSessionId,
        targetComponent,
      });
    } else {
      // 已经是UUID，直接使用
      finalSessionId = sessionId;
    }

    // 转发到ServiceCoordinator
    this.eventBus.emit('ai:send-chat-request-internal', {
      userInput,
      sessionId: finalSessionId,
      requestId,
    });
  }

  /**
   * 处理AI响应（纯UUID模式）
   */
  private handleAIResponse(data: { message: string; sessionId: string; requestId?: string }): void {
    const { message, sessionId } = data;

    this.logger.info('🤖 处理AI响应（纯UUID模式）', {
      messageLength: message.length,
      sessionId,
      currentScenario: this.routingState.currentScenario,
      routingStateSessionId: this.routingState.sessionId,
    });

    // 直接根据UUID查找对应的组件
    const target = this.getComponentBySessionId(sessionId);

    if (!target) {
      // 如果找不到映射，尝试使用当前场景
      const fallbackTarget =
        this.routingState.currentScenario === 'custom-component'
          ? 'custom-component'
          : 'greeting-page';

      this.logger.warn('⚠️ 未找到UUID对应的组件，使用当前场景', {
        sessionId,
        fallbackTarget,
        currentScenario: this.routingState.currentScenario,
      });

      // 路由到fallback目标
      this.routeAIResponseToTarget(fallbackTarget, sessionId, message, data.requestId);
      return;
    }

    this.logger.info('🎯 AI响应精确路由决策（UUID映射）', {
      target,
      sessionId,
      routingMethod: 'uuid-mapping-based',
    });

    // 路由到正确的目标
    this.routeAIResponseToTarget(target, sessionId, message, data.requestId);
  }

  /**
   * 将AI响应路由到指定目标
   */
  private routeAIResponseToTarget(
    target: MessageTarget,
    sessionId: string,
    message: string,
    requestId?: string
  ): void {
    // 路由消息
    this.routeMessage({
      messageType: 'ai-response',
      target,
      sessionId,
      content: message,
    });

    // 发送AI响应事件到目标
    const eventName = `${target}:ai-response`;
    const eventData = {
      message,
      sessionId,
      requestId,
    };

    this.logger.info('📤 发送AI响应事件', {
      eventName,
      eventData: { ...eventData, message: message.substring(0, 50) + '...' },
    });

    this.eventBus.emit(eventName, eventData);
  }

  /**
   * ASR路由决策：基于页面可见性进行智能路由
   */
  private makeASRRoutingDecision(): RoutingDecision {
    let target: MessageTarget;
    let reason: string;
    let targetSessionId: string;

    this.logger.info('🎯 开始ASR智能路由决策', {
      routingContext: this.routingContext,
      currentScenario: this.routingState.currentScenario,
    });

    // ASR智能路由逻辑：
    // 1. 优先路由到当前可见/活跃的组件
    // 2. 如果打招呼页面可见，路由到打招呼页面并使用其sessionId
    // 3. 否则路由到自定义组件并使用其sessionId

    if (this.routingContext.isGreetingPageVisible) {
      target = 'greeting-page';
      reason = '打招呼页面当前可见，路由到打招呼页面';
      // 为打招呼页面生成或获取专用sessionId
      targetSessionId = this.getOrCreateSessionIdForTarget('greeting-page');
    } else if (this.routingContext.hasCustomComponent) {
      target = 'custom-component';
      reason = '自定义组件已就绪，路由到自定义组件';
      // 为自定义组件生成或获取专用sessionId
      targetSessionId = this.getOrCreateSessionIdForTarget('custom-component');
    } else {
      // 默认路由到自定义组件，即使它还未完全就绪
      target = 'custom-component';
      reason = '默认路由到自定义组件';
      targetSessionId = this.getOrCreateSessionIdForTarget('custom-component');
    }

    this.logger.info('✅ ASR智能路由决策完成', {
      target,
      targetSessionId,
      reason,
      isGreetingPageVisible: this.routingContext.isGreetingPageVisible,
      hasCustomComponent: this.routingContext.hasCustomComponent,
    });

    return {
      target,
      sessionId: targetSessionId,
      reason,
    };
  }

  /**
   * 路由消息
   */
  private routeMessage(event: MessageRouteEvent): void {
    this.logger.debug('路由消息', event);
    this.eventBus.emit('router:message-routed', event);
  }

  /**
   * 切换场景
   */
  public switchScenario(scenario: ScenarioType, sessionId?: string): void {
    const previousScenario = this.routingState.currentScenario;

    // 如果场景相同但sessionId不同，仍需要更新sessionId
    if (previousScenario === scenario && sessionId && sessionId === this.routingState.sessionId) {
      this.logger.debug('场景和sessionId都未变化，跳过切换', { scenario, sessionId });
      return;
    }

    // 确定目标组件
    const target: MessageTarget =
      scenario === 'custom-component' ? 'custom-component' : 'greeting-page';

    // 如果提供了sessionId，将其注册到对应组件
    let newSessionId: string;
    if (sessionId) {
      newSessionId = sessionId;
      // 建立UUID到组件的映射
      this.componentSessionIds.set(target, sessionId);
      this.sessionIdToComponentMap.set(sessionId, target);

      this.logger.info('🔄 注册组件UUID会话ID', {
        target,
        sessionId,
        scenario,
      });
    } else {
      // 获取或创建组件专用的UUID
      newSessionId = this.getOrCreateSessionIdForTarget(target);
    }

    // 更新路由状态
    this.routingState = {
      currentScenario: scenario,
      sessionId: newSessionId,
      isActive: true,
    };

    this.logger.info('切换场景', {
      from: previousScenario,
      to: scenario,
      sessionId: this.routingState.sessionId,
      providedSessionId: sessionId,
      target,
      componentSessions: Array.from(this.componentSessionIds.entries()),
    });

    // 发送场景切换事件
    const changeEvent: RoutingChangeEvent = {
      from: previousScenario,
      to: scenario,
      sessionId: this.routingState.sessionId,
      timestamp: Date.now(),
    };

    this.eventBus.emit('router:scenario-changed', changeEvent);
  }

  /**
   * 更新路由上下文
   */
  private updateRoutingContext(updates: Partial<RoutingContext>): void {
    this.routingContext = { ...this.routingContext, ...updates };
    this.logger.debug('更新路由上下文', this.routingContext);
  }

  /**
   * 获取当前路由状态
   */
  public getRoutingState(): RoutingState {
    return { ...this.routingState };
  }

  /**
   * 获取路由上下文
   */
  public getRoutingContext(): RoutingContext {
    return { ...this.routingContext };
  }

  /**
   * 获取或创建目标组件的专用会话ID（纯UUID）
   */
  private getOrCreateSessionIdForTarget(target: MessageTarget): string {
    let sessionId = this.componentSessionIds.get(target);

    if (!sessionId) {
      // 直接生成纯UUID
      sessionId = generateUUID();

      // 建立映射关系
      this.componentSessionIds.set(target, sessionId);
      this.sessionIdToComponentMap.set(sessionId, target);

      this.logger.info('🆕 为组件创建新的UUID会话ID', {
        target,
        sessionId,
      });
    } else {
      this.logger.info('♻️ 使用现有的组件会话ID', {
        target,
        sessionId,
      });
    }

    return sessionId;
  }

  /**
   * 根据UUID获取对应的组件
   */
  private getComponentBySessionId(sessionId: string): MessageTarget | null {
    const component = this.sessionIdToComponentMap.get(sessionId);
    if (component) {
      return component;
    }

    // 如果没有找到直接映射，检查是否是组件的当前会话ID
    for (const [target, targetSessionId] of this.componentSessionIds.entries()) {
      if (targetSessionId === sessionId) {
        return target;
      }
    }

    return null;
  }

  /**
   * 清理组件的会话ID（纯UUID模式）
   */
  private clearComponentSession(target: MessageTarget): void {
    const sessionId = this.componentSessionIds.get(target);
    if (sessionId) {
      // 清理所有相关映射
      this.componentSessionIds.delete(target);
      this.sessionIdToComponentMap.delete(sessionId);

      this.logger.info('🧹 清理组件会话ID映射', {
        target,
        sessionId,
        remainingSessions: Array.from(this.componentSessionIds.entries()),
      });
    }
  }

  /**
   * 生成会话ID（保留原有方法用于兼容性）
   */
  private generateSessionId(): string {
    const prefix =
      this.routingState?.currentScenario === 'custom-component' ? 'custom' : 'greeting';
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 记录路由状态（调试用）
   */
  private logRoutingState(context: string): void {
    this.logger.debug(`🔍 路由状态检查 - ${context}`, {
      routingState: this.routingState,
      routingContext: this.routingContext,
      timestamp: Date.now(),
    });
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(): any {
    return {
      routingState: this.routingState,
      routingContext: this.routingContext,
      config: this.config,
      timestamp: Date.now(),
    };
  }

  /**
   * 销毁路由器
   */
  public destroy(): void {
    this.logger.info('消息路由器已销毁');
  }
}
