/**
 * SSE (Server-Sent Events) 传输层实现
 * 用于处理AI服务器的流式响应
 */

import { EventBus } from '../core/EventBus';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * SSE传输配置
 */
export interface SSETransportConfig {
  /** 基础URL */
  baseURL: string;
  /** 连接超时时间（毫秒） */
  timeout?: number;
  /** 重连配置 */
  reconnect?: {
    /** 是否启用自动重连 */
    enabled: boolean;
    /** 最大重连次数 */
    maxAttempts: number;
    /** 重连间隔（毫秒） */
    delay: number;
  };
  /** 请求头 */
  headers?: Record<string, string>;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * SSE连接状态
 */
export enum SSETransportState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  FAILED = 'failed',
}

/**
 * SSE传输事件
 */
export interface SSETransportEvents {
  'state-change': { state: SSETransportState; error?: Error };
  message: { data: string; event?: string };
  error: { error: Error };
}

/**
 * SSE传输层实现
 */
export class SSETransport {
  private config: Required<SSETransportConfig>;
  private eventBus: EventBus;
  private logger: Logger;
  private eventSource: EventSource | null = null;
  private state: SSETransportState = SSETransportState.DISCONNECTED;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;

  constructor(config: SSETransportConfig, eventBus: EventBus) {
    this.config = {
      timeout: 30000,
      reconnect: {
        enabled: true,
        maxAttempts: 5,
        delay: 3000,
      },
      headers: {},
      debug: false,
      ...config,
    };

    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'SSETransport',
    });
  }

  /**
   * 连接到SSE端点
   */
  public async connect(endpoint: string, params?: Record<string, string>): Promise<void> {
    if (this.eventSource) {
      this.logger.warn('SSE已连接，跳过重复连接');
      return;
    }

    return new Promise((resolve, reject) => {
      try {
        // 构建URL
        const url = new URL(endpoint, this.config.baseURL);
        if (params) {
          Object.entries(params).forEach(([key, value]) => {
            url.searchParams.set(key, value);
          });
        }

        this.logger.info('连接SSE端点', { url: url.toString() });
        this.setState(SSETransportState.CONNECTING);

        // 创建EventSource实例
        this.eventSource = new EventSource(url.toString());

        // 设置连接超时
        const timeout = setTimeout(() => {
          reject(new Error('SSE连接超时'));
        }, this.config.timeout);

        // 绑定事件处理器
        this.eventSource.onopen = () => {
          clearTimeout(timeout);
          this.setState(SSETransportState.CONNECTED);
          this.reconnectAttempts = 0;
          this.logger.info('SSE连接建立成功');
          resolve();
        };

        this.eventSource.onmessage = event => {
          // 只在调试模式下记录原始数据，减少日志噪音
          this.logger.debug('📨 收到SSE原始数据', { data: event.data });
          this.handleMessage(event.data, event.type);
        };

        this.eventSource.onerror = () => {
          clearTimeout(timeout);

          if (this.eventSource?.readyState === EventSource.CLOSED) {
            // 连接已关闭
            this.setState(SSETransportState.DISCONNECTED);
            this.scheduleReconnect();
          } else {
            // 连接错误
            const error = new Error('SSE连接错误');
            this.logger.error('SSE连接错误', error);
            this.setState(SSETransportState.FAILED, error);
            this.emitEvent('error', { error });
            reject(error);
          }
        };
      } catch (error) {
        const err = error as Error;
        this.logger.error('创建SSE连接失败', err);
        this.setState(SSETransportState.FAILED, err);
        reject(err);
      }
    });
  }

  /**
   * 断开SSE连接
   */
  public disconnect(): void {
    if (this.eventSource) {
      this.logger.info('断开SSE连接');
      this.eventSource.close();
      this.eventSource = null;
      this.setState(SSETransportState.DISCONNECTED);
    }

    // 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): SSETransportState {
    return this.state;
  }

  /**
   * 检查是否已连接
   */
  public isConnected(): boolean {
    return (
      this.state === SSETransportState.CONNECTED &&
      this.eventSource?.readyState === EventSource.OPEN
    );
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: string, event?: string): void {
    try {
      this.logger.debug('收到SSE消息', { data, event });
      const eventData: { data: string; event?: string } = { data };
      if (event !== undefined) {
        eventData.event = event;
      }
      this.emitEvent('message', eventData);
    } catch (error) {
      this.logger.error('处理SSE消息失败', { error, data });
    }
  }

  /**
   * 设置传输状态
   */
  private setState(newState: SSETransportState, error?: Error): void {
    if (this.state !== newState) {
      const previousState = this.state;
      this.state = newState;

      this.logger.debug('SSE状态变化', {
        from: previousState,
        to: newState,
        error: error?.message,
      });

      const eventData: { state: SSETransportState; error?: Error } = { state: newState };
      if (error !== undefined) {
        eventData.error = error;
      }
      this.emitEvent('state-change', eventData);
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (
      !this.config.reconnect.enabled ||
      this.reconnectAttempts >= this.config.reconnect.maxAttempts
    ) {
      this.logger.warn('SSE重连已禁用或达到最大重连次数');
      return;
    }

    this.reconnectAttempts++;
    this.setState(SSETransportState.RECONNECTING);

    this.logger.info('安排SSE重连', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.reconnect.maxAttempts,
      delay: this.config.reconnect.delay,
    });

    this.reconnectTimer = setTimeout(() => {
      this.logger.warn('SSE重连功能暂未完全实现，标记为失败状态');
      this.setState(SSETransportState.FAILED, new Error('SSE重连功能未实现'));
      this.emitEvent('error', { error: new Error('SSE重连功能未实现') });
    }, this.config.reconnect.delay);
  }

  /**
   * 发送事件到事件总线
   */
  private emitEvent<K extends keyof SSETransportEvents>(
    event: K,
    data: SSETransportEvents[K]
  ): void {
    this.eventBus.emit(`sse:${event}`, data);
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<SSETransportConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('SSE传输配置已更新', newConfig);
  }

  /**
   * 获取当前配置
   */
  public getConfig(): SSETransportConfig {
    return { ...this.config };
  }

  /**
   * 销毁传输层
   */
  public destroy(): void {
    this.disconnect();
    this.logger.info('SSE传输层已销毁');
  }
}
