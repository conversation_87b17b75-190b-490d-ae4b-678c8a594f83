/**
 * 会话服务 - 管理会话生命周期
 */

import { generateRequestId } from '../utils/helpers';
import { Logger, LogLevel } from '../utils/Logger';

import { ISessionService, EventCallback, UnsubscribeFunction } from './types';

export class SessionService implements ISessionService {
  private currentSessionId: string | null = null;
  private sessions = new Set<string>();
  private sessionCallbacks = new Set<EventCallback<string | null>>();
  private logger: Logger;
  private sessionTimeout: number;
  private sessionTimers = new Map<string, NodeJS.Timeout>();

  constructor(config?: { sessionTimeout?: number; autoCreate?: boolean }) {
    this.logger = Logger.getInstance({ level: LogLevel.INFO, prefix: 'SessionService' });
    this.sessionTimeout = config?.sessionTimeout || 30 * 60 * 1000; // 默认30分钟

    if (config?.autoCreate !== false) {
      // 默认自动创建会话
      this.createSession();
    }
  }

  /**
   * 创建新会话
   */
  createSession(): string {
    const sessionId = `session_${Date.now()}_${generateRequestId().slice(-8)}`;
    this.sessions.add(sessionId);

    // 设置会话超时
    this.setSessionTimeout(sessionId);

    // 如果没有当前会话，设置为当前会话
    if (!this.currentSessionId) {
      this.switchSession(sessionId);
    }

    this.logger.info(`创建新会话: ${sessionId}`);
    return sessionId;
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): string | null {
    return this.currentSessionId;
  }

  /**
   * 切换会话
   */
  switchSession(sessionId: string): void {
    if (!this.sessions.has(sessionId)) {
      this.logger.warn(`会话 ${sessionId} 不存在，无法切换`);
      return;
    }

    const oldSessionId = this.currentSessionId;
    this.currentSessionId = sessionId;

    // 重置会话超时
    this.setSessionTimeout(sessionId);

    this.logger.info(`切换会话: ${oldSessionId} -> ${sessionId}`);
    this.notifySessionChange(sessionId);
  }

  /**
   * 结束会话
   */
  endSession(sessionId: string): void {
    if (!this.sessions.has(sessionId)) {
      this.logger.warn(`会话 ${sessionId} 不存在，无法结束`);
      return;
    }

    this.sessions.delete(sessionId);

    // 清除会话超时定时器
    const timer = this.sessionTimers.get(sessionId);
    if (timer) {
      clearTimeout(timer);
      this.sessionTimers.delete(sessionId);
    }

    // 如果结束的是当前会话，清空当前会话
    if (this.currentSessionId === sessionId) {
      this.currentSessionId = null;
      this.notifySessionChange(null);
    }

    this.logger.info(`结束会话: ${sessionId}`);
  }

  /**
   * 监听会话变化
   */
  onSessionChange(callback: EventCallback<string | null>): UnsubscribeFunction {
    this.sessionCallbacks.add(callback);

    return () => {
      this.sessionCallbacks.delete(callback);
    };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.logger.info('销毁会话服务');

    // 清除所有定时器
    for (const timer of this.sessionTimers.values()) {
      clearTimeout(timer);
    }
    this.sessionTimers.clear();

    // 清空所有数据
    this.sessions.clear();
    this.sessionCallbacks.clear();
    this.currentSessionId = null;
  }

  /**
   * 获取所有会话ID
   */
  getAllSessions(): string[] {
    return Array.from(this.sessions);
  }

  /**
   * 检查会话是否存在
   */
  hasSession(sessionId: string): boolean {
    return this.sessions.has(sessionId);
  }

  /**
   * 刷新会话超时
   */
  refreshSession(sessionId?: string): void {
    const targetSessionId = sessionId || this.currentSessionId;
    if (targetSessionId && this.sessions.has(targetSessionId)) {
      this.setSessionTimeout(targetSessionId);
    }
  }

  /**
   * 设置会话超时
   */
  private setSessionTimeout(sessionId: string): void {
    // 清除现有定时器
    const existingTimer = this.sessionTimers.get(sessionId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      this.logger.info(`会话 ${sessionId} 超时，自动结束`);
      this.endSession(sessionId);
    }, this.sessionTimeout);

    this.sessionTimers.set(sessionId, timer);
  }

  /**
   * 通知会话变化
   */
  private notifySessionChange(sessionId: string | null): void {
    for (const callback of this.sessionCallbacks) {
      try {
        callback(sessionId);
      } catch (error) {
        this.logger.error('会话变化回调执行出错:', error);
      }
    }
  }
}
