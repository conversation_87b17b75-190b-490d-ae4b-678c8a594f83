<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSDK 简单演示</title>
</head>

<body>
    <!-- 聊天组件容器 -->
    <chat-widget theme="light"></chat-widget>
    <!-- 加载SDK -->
    <script src="../dist/web-service-sdk.js"></script>

    <script>
        // 处理新用户事件
        function handleNewUser(userData) {

            const shouldShow = confirm('检测到新用户进入，是否显示打招呼页面？');

            if (shouldShow) {
                window.sdk.showGreetingPage();
            } else {
                console.log('用户选择跳过打招呼页面');
            }
        }

        // 极简初始化 - 自动启动，直接可用
        WebServiceSDK.init({
            hksttUrl: 'ws://localhost:8001',           // HKSTT服务地址
            aiServerUrl: 'http://localhost:8002',      // AI服务地址
            ttsUrl: 'http://*************:8080', // TTS服务地址（可选，启用语音播放）
            debug: true                                // 开启调试模式
        }).then(sdk => {
            // 保存到全局变量
            window.sdk = sdk;

            sdk.onNotification('notifications/newUser', (params) => {
                console.log('检测到新用户:', params);
                handleNewUser(params);
            });

        }); 
    </script>
</body>

</html>