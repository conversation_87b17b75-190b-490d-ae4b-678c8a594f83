/**
 * HKSTT服务客户端
 * 连接到真实的HKSTT WebSocket服务器
 */

import { EventBus } from '../core/EventBus';
import { JsonRpcMessageHandler } from '../jsonrpc/JsonRpcMessageHandler';
import { SimpleWebSocketTransport } from '../transport/SimpleWebSocketTransport';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * HKSTT客户端配置
 */
export interface HKSTTClientConfig {
  /** HKSTT服务器URL */
  url: string;
  /** 连接超时时间（毫秒） */
  connectionTimeout?: number;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * HKSTT客户端状态
 */
export enum HKSTTClientState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  FAILED = 'failed',
}

/**
 * HKSTT服务事件
 */
export interface HKSTTServiceEvents {
  'service-ready': { service: string; version: string; capabilities: string[] };
  'face-status': { hasFace: boolean };
  'asr-offline-result': { sid: string; text: string };
  'asr-session-complete': Record<string, never>;
  'model-status': { loaded: boolean };
}

/**
 * HKSTT服务客户端
 */
export class HKSTTClient {
  private config: Required<HKSTTClientConfig>;
  private eventBus: EventBus;
  private logger: Logger;
  private transport: SimpleWebSocketTransport;
  private jsonRpcHandler: JsonRpcMessageHandler;
  private state: HKSTTClientState = HKSTTClientState.DISCONNECTED;

  constructor(config: HKSTTClientConfig, eventBus: EventBus) {
    this.config = {
      connectionTimeout: 10000,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      debug: false,
      ...config,
    };

    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'HKSTTClient',
    });

    // 创建JSON-RPC消息处理器
    this.jsonRpcHandler = new JsonRpcMessageHandler();

    // 创建简化WebSocket传输层
    this.transport = new SimpleWebSocketTransport(
      {
        url: this.config.url,
        connectionTimeout: this.config.connectionTimeout,
        maxReconnectAttempts: this.config.maxReconnectAttempts,
        reconnectInterval: this.config.reconnectInterval,
      },
      this.eventBus
    );

    this.bindEvents();
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听传输层状态变化
    this.eventBus.on('transport:state-change', (data: any) => {
      this.handleTransportStateChange(data.state, data.error);
    });

    // 监听传输层消息
    this.eventBus.on('message', (data: any) => {
      this.handleMessage(data.data);
    });

    // 监听传输层错误
    this.eventBus.on('transport:error', (data: any) => {
      this.handleTransportError(data.error);
    });
  }

  /**
   * 连接到HKSTT服务器
   */
  public async connect(): Promise<void> {
    if (this.state === HKSTTClientState.CONNECTED) {
      this.logger.warn('HKSTT客户端已连接，跳过重复连接');
      return;
    }

    try {
      this.logger.info('连接HKSTT服务器', { url: this.config.url });
      this.setState(HKSTTClientState.CONNECTING);

      await this.transport.connect();

      // 连接成功后立即设置状态为CONNECTED
      this.setState(HKSTTClientState.CONNECTED);
      this.logger.info('HKSTT客户端连接成功');
    } catch (error) {
      this.logger.error('HKSTT客户端连接失败', error);
      this.setState(HKSTTClientState.FAILED, error as Error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    this.logger.info('断开HKSTT客户端连接');
    this.transport.disconnect();
    this.setState(HKSTTClientState.DISCONNECTED);
  }

  /**
   * 发送ping请求
   */
  public async ping(): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('HKSTT客户端未连接');
    }

    const request = {
      jsonrpc: '2.0',
      method: 'ping',
      params: {},
      id: `ping-${Date.now()}`,
    };

    try {
      this.transport.send(JSON.stringify(request));
      this.logger.debug('发送ping请求', { id: request.id });
    } catch (error) {
      this.logger.error('发送ping请求失败', error);
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  public async getStatus(): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('HKSTT客户端未连接');
    }

    const request = {
      jsonrpc: '2.0',
      method: 'getStatus',
      params: {},
      id: `status-${Date.now()}`,
    };

    try {
      this.transport.send(JSON.stringify(request));
      this.logger.debug('发送状态查询请求', { id: request.id });
    } catch (error) {
      this.logger.error('发送状态查询请求失败', error);
      throw error;
    }
  }

  /**
   * 启动收音
   */
  public async startAudio(): Promise<void> {
    const request = {
      jsonrpc: '2.0',
      method: 'startAudio',
      params: {},
      id: `start-audio-${Date.now()}`,
    };

    try {
      // 使用简化的传输层发送
      this.transport.send(JSON.stringify(request));
      this.logger.info('发送启动收音请求', { id: request.id });
    } catch (error) {
      this.logger.error('发送启动收音请求失败', error);
      throw error;
    }
  }

  /**
   * 结束收音
   */
  public async endAudio(): Promise<void> {
    const request = {
      jsonrpc: '2.0',
      method: 'endAudio',
      params: {},
      id: `end-audio-${Date.now()}`,
    };

    try {
      // 使用简化的传输层发送
      this.transport.send(JSON.stringify(request));
      this.logger.info('发送结束收音请求', { id: request.id });
    } catch (error) {
      this.logger.error('发送结束收音请求失败', error);
      throw error;
    }
  }

  /**
   * 处理传输层状态变化
   */
  private handleTransportStateChange(transportState: string, error?: Error): void {
    this.logger.info('处理传输层状态变化', {
      transportState,
      currentClientState: this.state,
      error: error?.message,
    });

    switch (transportState) {
      case 'connecting':
        this.setState(HKSTTClientState.CONNECTING);
        break;
      case 'connected':
        this.setState(HKSTTClientState.CONNECTED);
        break;
      case 'reconnecting':
        this.setState(HKSTTClientState.RECONNECTING);
        break;
      case 'failed':
        this.setState(HKSTTClientState.FAILED, error);
        break;
      case 'disconnected':
        this.setState(HKSTTClientState.DISCONNECTED);
        break;
    }
  }

  /**
   * 处理传输层错误
   */
  private handleTransportError(error: Error): void {
    this.logger.error('HKSTT传输层错误', error);
    this.emitEvent('error', { error });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: string): void {
    try {
      this.logger.debug('收到HKSTT消息', { data });

      // 使用JsonRpcMessageHandler解析消息
      const message = this.jsonRpcHandler.parseMessage(data);

      if (!message) {
        this.logger.warn('收到无效的JSON-RPC消息', { data });
        return;
      }

      // 处理不同类型的消息
      if (this.jsonRpcHandler.isNotification(message)) {
        this.handleNotification(message);
      } else if (
        this.jsonRpcHandler.isResponse(message) ||
        this.jsonRpcHandler.isErrorResponse(message)
      ) {
        this.handleResponse(message);
      } else {
        this.logger.warn('收到未知类型的消息', { message });
      }
    } catch (error) {
      this.logger.error('处理HKSTT消息失败', { error, data });
    }
  }

  /**
   * 处理通知消息
   */
  private handleNotification(notification: any): void {
    const { method, params } = notification;

    this.logger.debug('处理HKSTT通知', { method, params });

    switch (method) {
      case 'notifications/serviceReady':
        this.handleServiceReady(params);
        break;
      case 'notifications/faceStatus':
        this.handleFaceStatus(params);
        break;
      case 'notifications/asrOfflineResult':
        this.handleASROfflineResult(params);
        break;
      case 'notifications/asrSessionComplete':
        this.handleASRSessionComplete(params);
        break;
      case 'notifications/modelStatus':
        this.handleModelStatus(params);
        break;
      default:
        this.logger.warn('收到未知的HKSTT通知', { method, params });
    }
  }

  /**
   * 处理响应消息
   */
  private handleResponse(response: any): void {
    const { id, result, error } = response;

    this.logger.debug('收到HKSTT响应', { id, result, error });

    if (error) {
      this.logger.error('HKSTT请求失败', { id, error });
    } else {
      this.logger.debug('HKSTT请求成功', { id, result });
    }

    // 发送响应事件
    this.emitEvent('response', { id, result, error });
  }

  /**
   * 处理服务就绪通知
   */
  private handleServiceReady(params: any): void {
    this.logger.info('HKSTT服务就绪', params);
    this.emitServiceEvent('service-ready', params);
  }

  /**
   * 处理人脸状态通知
   */
  private handleFaceStatus(params: any): void {
    this.logger.info('收到人脸状态', params);
    this.emitServiceEvent('face-status', params);
  }

  /**
   * 处理ASR离线识别结果
   */
  private handleASROfflineResult(params: any): void {
    this.logger.info('收到ASR识别结果', {
      sid: params.sid,
      text: params.text?.substring(0, 50) + '...',
    });
    this.emitServiceEvent('asr-offline-result', params);
  }

  /**
   * 处理ASR会话完成
   */
  private handleASRSessionComplete(params: any): void {
    this.logger.info('ASR会话完成', params);
    this.emitServiceEvent('asr-session-complete', params);
  }

  /**
   * 处理模型状态
   */
  private handleModelStatus(params: any): void {
    this.logger.info('收到模型状态', params);
    this.emitServiceEvent('model-status', params);
  }

  /**
   * 设置客户端状态
   */
  private setState(newState: HKSTTClientState, error?: Error): void {
    if (this.state !== newState) {
      const previousState = this.state;
      this.state = newState;

      this.logger.debug('HKSTT客户端状态变化', {
        from: previousState,
        to: newState,
        error: error?.message,
      });

      this.emitEvent('state-change', { state: newState, error });
    }
  }

  /**
   * 发送服务事件
   */
  private emitServiceEvent<K extends keyof HKSTTServiceEvents>(
    event: K,
    data: HKSTTServiceEvents[K]
  ): void {
    this.eventBus.emit(`hkstt:${event}`, data);
  }

  /**
   * 发送客户端事件
   */
  private emitEvent(event: string, data: any): void {
    this.eventBus.emit(`hkstt-client:${event}`, data);
  }

  /**
   * 获取当前状态
   */
  public getState(): HKSTTClientState {
    return this.state;
  }

  /**
   * 检查是否已连接
   */
  public isConnected(): boolean {
    // 简化连接检查：直接使用传输层的连接状态
    return this.transport.isConnected();
  }

  /**
   * 销毁客户端
   */
  public destroy(): void {
    this.disconnect();
    this.transport.destroy();
    this.logger.info('HKSTT客户端已销毁');
  }
}
