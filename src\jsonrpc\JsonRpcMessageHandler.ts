/**
 * 轻量级JSON-RPC消息处理器
 * 只负责消息格式的解析和构建，不包含路由和状态管理
 */

/**
 * JSON-RPC 2.0 消息类型
 */
export interface JsonRpcMessage {
  jsonrpc: '2.0';
}

/**
 * JSON-RPC 2.0 请求
 */
export interface JsonRpcRequest extends JsonRpcMessage {
  method: string;
  params?: any; // JSON-RPC 标准：params 可以是任意 JSON 值
  id: string | number;
}

/**
 * JSON-RPC 2.0 响应
 */
export interface JsonRpcResponse extends JsonRpcMessage {
  result: any; // JSON-RPC 标准：result 可以是任意 JSON 值
  id: string | number;
}

/**
 * JSON-RPC 2.0 错误响应
 */
export interface JsonRpcErrorResponse extends JsonRpcMessage {
  error: {
    code: number;
    message: string;
    data?: any; // JSON-RPC 标准：error.data 可以是任意 JSON 值
  };
  id: string | number;
}

/**
 * JSON-RPC 2.0 通知
 */
export interface JsonRpcNotification extends JsonRpcMessage {
  method: string;
  params?: any; // JSON-RPC 标准：params 可以是任意 JSON 值
}

/**
 * JSON-RPC 错误码
 */
export enum JsonRpcErrorCode {
  PARSE_ERROR = -32700,
  INVALID_REQUEST = -32600,
  METHOD_NOT_FOUND = -32601,
  INVALID_PARAMS = -32602,
  INTERNAL_ERROR = -32603,
}

/**
 * 轻量级JSON-RPC消息处理器
 */
export class JsonRpcMessageHandler {
  /**
   * 解析JSON-RPC消息
   */
  public parseMessage(data: string): JsonRpcMessage | null {
    try {
      const message = JSON.parse(data);

      // 基本格式验证
      if (!message || typeof message !== 'object' || message.jsonrpc !== '2.0') {
        return null;
      }

      return message as JsonRpcMessage;
    } catch {
      return null;
    }
  }

  /**
   * 创建JSON-RPC请求
   */
  public createRequest(
    method: string,
    params?: any /* JSON-RPC 标准参数 */,
    id?: string | number
  ): JsonRpcRequest {
    return {
      jsonrpc: '2.0',
      method,
      params,
      id: id || this.generateId(),
    };
  }

  /**
   * 创建JSON-RPC响应
   */
  public createResponse(result: any /* JSON-RPC 标准结果 */, id: string | number): JsonRpcResponse {
    return {
      jsonrpc: '2.0',
      result,
      id,
    };
  }

  /**
   * 创建JSON-RPC通知
   */
  public createNotification(method: string, params?: any): JsonRpcNotification {
    return {
      jsonrpc: '2.0',
      method,
      params,
    };
  }

  /**
   * 创建JSON-RPC错误响应
   */
  public createError(
    code: number,
    message: string,
    id: string | number,
    data?: any
  ): JsonRpcErrorResponse {
    return {
      jsonrpc: '2.0',
      error: {
        code,
        message,
        data,
      },
      id,
    };
  }

  /**
   * 判断是否为请求消息
   */
  public isRequest(message: JsonRpcMessage): message is JsonRpcRequest {
    return 'method' in message && 'id' in message;
  }

  /**
   * 判断是否为响应消息
   */
  public isResponse(message: JsonRpcMessage): message is JsonRpcResponse {
    return 'result' in message && 'id' in message;
  }

  /**
   * 判断是否为错误响应消息
   */
  public isErrorResponse(message: JsonRpcMessage): message is JsonRpcErrorResponse {
    return 'error' in message && 'id' in message;
  }

  /**
   * 判断是否为通知消息
   */
  public isNotification(message: JsonRpcMessage): message is JsonRpcNotification {
    return 'method' in message && !('id' in message);
  }

  /**
   * 生成请求ID
   */
  private generateId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 序列化消息为JSON字符串
   */
  public stringify(message: JsonRpcMessage): string {
    return JSON.stringify(message);
  }

  /**
   * 验证消息格式
   */
  public validateMessage(message: any): boolean {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (message.jsonrpc !== '2.0') {
      return false;
    }

    // 验证请求格式
    if ('method' in message && 'id' in message) {
      return (
        typeof message.method === 'string' &&
        (typeof message.id === 'string' || typeof message.id === 'number')
      );
    }

    // 验证响应格式
    if ('result' in message && 'id' in message) {
      return typeof message.id === 'string' || typeof message.id === 'number';
    }

    // 验证错误响应格式
    if ('error' in message && 'id' in message) {
      return (
        message.error &&
        typeof message.error.code === 'number' &&
        typeof message.error.message === 'string' &&
        (typeof message.id === 'string' || typeof message.id === 'number')
      );
    }

    // 验证通知格式
    if ('method' in message && !('id' in message)) {
      return typeof message.method === 'string';
    }

    return false;
  }
}
