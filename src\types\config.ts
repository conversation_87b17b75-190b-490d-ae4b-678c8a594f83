/**
 * SDK配置 - 极简版本
 * 只保留核心必需配置，其他都使用合理默认值
 */

/**
 * SDK配置接口 - 只要两个服务地址即可
 */
export interface WebSDKConfig {
  /** HKSTT服务器URL */
  hksttUrl: string;
  /** AI服务器URL */
  aiServerUrl: string;
  /** TTS服务器URL（可选，如果提供则自动启用TTS功能） */
  ttsUrl?: string;
  /** 调试模式（可选，默认false） */
  debug?: boolean;
  /** 静态文件基础路径（可选，默认为相对于SDK文件的./static） */
  staticBasePath?: string;
}

/**
 * 内部配置接口 - 包含所有默认值
 */
export interface InternalWebSDKConfig {
  hksttUrl: string;
  aiServerUrl: string;
  ttsUrl?: string;
  debug: boolean;
  staticBasePath: string;
  // 所有其他配置都使用固定的合理默认值
}

/**
 * 配置工具函数
 */

/**
 * 获取SDK脚本的基础路径
 */
function getSDKBasePath(): string {
  // 尝试从当前脚本标签获取路径
  const scripts = document.getElementsByTagName('script');
  for (let i = scripts.length - 1; i >= 0; i--) {
    const script = scripts[i];
    if (script && script.src) {
      const src = script.src;
      if (src.includes('web-service-sdk.js')) {
        // 获取脚本所在目录
        const lastSlash = src.lastIndexOf('/');
        return lastSlash > 0 ? src.substring(0, lastSlash) : '.';
      }
    }
  }

  // 如果找不到脚本，使用当前页面的基础路径
  const baseElement = document.querySelector('base');
  if (baseElement && baseElement.href) {
    return baseElement.href.replace(/\/$/, '');
  }

  // 最后的回退方案
  return '.';
}

/**
 * 创建内部配置 - 使用固定的合理默认值
 */
export function createInternalConfig(userConfig: WebSDKConfig): InternalWebSDKConfig {
  const sdkBasePath = getSDKBasePath();
  const defaultStaticPath = `${sdkBasePath}/static`;

  const config: InternalWebSDKConfig = {
    hksttUrl: userConfig.hksttUrl,
    aiServerUrl: userConfig.aiServerUrl,
    debug: userConfig.debug ?? false,
    staticBasePath: userConfig.staticBasePath ?? defaultStaticPath,
  };

  // 只有当ttsUrl存在时才添加到配置中
  if (userConfig.ttsUrl) {
    config.ttsUrl = userConfig.ttsUrl;
  }

  return config;
}

/**
 * 验证配置 - 只验证必需项
 */
export function validateConfig(config: WebSDKConfig): void {
  if (!config.hksttUrl) {
    throw new Error('HKSTT服务器URL是必需的');
  }

  if (!config.aiServerUrl) {
    throw new Error('AI服务器URL是必需的');
  }

  // 验证URL格式
  try {
    new URL(config.hksttUrl);
  } catch {
    throw new Error('HKSTT服务器URL格式无效');
  }

  try {
    new URL(config.aiServerUrl);
  } catch {
    throw new Error('AI服务器URL格式无效');
  }

  // 验证TTS URL格式（如果提供）
  if (config.ttsUrl) {
    try {
      new URL(config.ttsUrl);
    } catch {
      throw new Error('TTS服务器URL格式无效');
    }
  }
}
